# Energy Consumption Test Guide

## Testing the Auto Work Energy Consumption Fix

### Prerequisites
1. Server is running
2. User has premium status (required for auto mode)
3. Factory exists and has available worker slots
4. User has sufficient energy (at least 10 energy)

### Test Steps

#### 1. Manual Energy Consumption Test
```bash
# Check user's current energy
GET /users/energy
# Note the current energy value

# Work at factory manually
POST /factories/{factoryId}/work
{
  "energySpent": 20
}

# Check energy again - should be reduced by 20
GET /users/energy
```

#### 2. Auto Work Energy Consumption Test
```bash
# Check user's current energy
GET /users/energy
# Note the current energy value (e.g., 80)

# Enable auto work mode
POST /factories/{factoryId}/auto-work
{
  "enable": true
}

# Wait for auto work to execute (check logs)
# Look for these log messages:
# - "[WORK] Before consumption - User ID: X, Energy: Y, Amount to consume: Z"
# - "[WORK] After consumption - User ID: X, Remaining energy: W"
# - "Auto work successful for user X at factory Y. Energy consumed: Z, Remaining energy: W"

# Check energy after auto work
GET /users/energy
# Energy should be reduced by the amount consumed
```

#### 3. Server Restart Test
```bash
# Enable auto work mode
POST /factories/{factoryId}/auto-work
{
  "enable": true
}

# Restart the server
# Check logs for restoration messages:
# - "Found X active work auto settings in database"
# - "Restored auto work for user X on target Y"

# Verify auto work continues after restart
```

### Expected Log Messages

#### Successful Energy Consumption:
```
[WorkAutoHandler] Executing auto work for user 25 at factory 164 with 50 energy
[WORK] Before consumption - User ID: 25, Energy: 50, Amount to consume: 50
[WORK] After consumption - User ID: 25, Remaining energy: 0
[WorkAutoHandler] Auto work successful for user 25 at factory 164. Energy consumed: 50, Remaining energy: 0
```

#### Insufficient Energy:
```
[WorkAutoHandler] Executing auto work for user 25 at factory 164 with 60 energy
[WORK] Before consumption - User ID: 25, Energy: 30, Amount to consume: 60
[WorkAutoHandler] Error executing auto work for user 25 at factory 164: Insufficient energy: User has 30 but needs 60
```

#### Auto Mode Restoration:
```
[AutoActionService] Found 1 active work auto settings in database
[AutoActionService] Restored auto work for user 25 on target 164
```

### Verification Points

1. **Energy is consumed exactly once per work session**
   - Check that energy decreases by the exact amount specified
   - No double consumption or race conditions

2. **Work sessions are created**
   - Check database for new WorkSession records
   - Verify energySpent field matches consumed energy

3. **Transaction atomicity**
   - If any error occurs, energy should not be consumed
   - All changes should be rolled back together

4. **Auto mode persistence**
   - Auto mode should survive server restarts
   - Database should show activeAutoMode = 'work'

5. **Proper error handling**
   - Insufficient energy should not consume any energy
   - Factory capacity limits should be respected
   - Premium status should be enforced

### Troubleshooting

#### If energy is not being consumed:
1. Check for SQL errors in logs (should be fixed now)
2. Verify transaction is completing successfully
3. Check that user has sufficient energy before consumption
4. Ensure factory exists and has capacity

#### If auto mode stops unexpectedly:
1. Check for validity check failures
2. Verify user still has premium status
3. Check factory capacity limits
4. Look for database connection issues

#### If server restart doesn't restore auto mode:
1. Check database for activeAutoMode field
2. Verify getActiveAutoSettings() is querying database correctly
3. Check for expired auto mode (24-hour limit)

### Database Queries for Verification

```sql
-- Check user's current energy and auto mode status
SELECT id, username, energy, "activeAutoMode", "autoTargetId", "autoModeExpiresAt" 
FROM "user" 
WHERE id = {userId};

-- Check recent work sessions
SELECT * FROM "work_session" 
WHERE "workerId" = {userId} 
ORDER BY "createdAt" DESC 
LIMIT 5;

-- Check active auto work users
SELECT id, username, energy, "activeAutoMode", "autoTargetId" 
FROM "user" 
WHERE "activeAutoMode" = 'work';
```
